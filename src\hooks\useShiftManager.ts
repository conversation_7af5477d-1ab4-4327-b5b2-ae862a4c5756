import { useState, useEffect, useCallback } from "react";
import { Shift, ShiftEntry } from "@/types/pos";
import {
  useLazyGetShiftsQuery,
  useLazyGetShiftEntriesQuery
} from "@/redux/slices/shifts";
import { useAuthHook } from "@/utils/useAuthHook";

interface ShiftState {
  currentShift: Shift | null;
  currentShiftEntry: ShiftEntry | null;
  elapsedTime: string;
  isActive: boolean;
  startTime: Date | null;
}

interface UseShiftManagerReturn {
  shiftState: ShiftState;
  updateShift: (shift: Shift | null, shiftEntry?: ShiftEntry | null) => void;
  clearShift: () => void;
  refreshShift: () => Promise<void>;
  formatElapsedTime: (startTime: string) => string;
}

const STORAGE_KEY = "currentShift";

export const useShiftManager = (): UseShiftManagerReturn => {
  const { user_details } = useAuthHook();
  const [getShifts] = useLazyGetShiftsQuery();
  const [getShiftEntries] = useLazyGetShiftEntriesQuery();

  const [shiftState, setShiftState] = useState<ShiftState>({
    currentShift: null,
    currentShiftEntry: null,
    elapsedTime: "00:00:00",
    isActive: false,
    startTime: null,
  });

  // Calculate elapsed time
  const calculateElapsedTime = useCallback((startTime: string): string => {
    const start = new Date(startTime);
    const now = new Date();
    const diff = now.getTime() - start.getTime();

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }, []);

  // Format elapsed time helper
  const formatElapsedTime = useCallback((startTime: string): string => {
    return calculateElapsedTime(startTime);
  }, [calculateElapsedTime]);

  // Load shift data from localStorage
  const loadShiftFromStorage = useCallback((): { shift: Shift | null; shiftEntry: ShiftEntry | null } => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        return {
          shift: data.shift || null,
          shiftEntry: data.shiftEntry || null
        };
      }
    } catch (error) {
      console.error("Error loading shift from storage:", error);
      localStorage.removeItem(STORAGE_KEY);
    }
    return { shift: null, shiftEntry: null };
  }, []);

  // Save shift data to localStorage
  const saveShiftToStorage = useCallback((data: { shift: Shift | null; shiftEntry: ShiftEntry | null }): void => {
    try {
      if (data.shift || data.shiftEntry) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
      } else {
        localStorage.removeItem(STORAGE_KEY);
      }
    } catch (error) {
      console.error("Error saving shift to storage:", error);
    }
  }, []);

  // Update shift state
  const updateShift = useCallback((shift: Shift | null, shiftEntry?: ShiftEntry | null): void => {
    const activeEntry = shiftEntry || null;
    const isActive = !!activeEntry && !activeEntry.end_time;
    const startTime = activeEntry ? new Date(activeEntry.start_time) : null;

    setShiftState(prev => ({
      ...prev,
      currentShift: shift,
      currentShiftEntry: activeEntry,
      isActive,
      startTime,
    }));

    // Save both shift and shift entry to storage
    const dataToSave = {
      shift,
      shiftEntry: activeEntry
    };
    saveShiftToStorage(dataToSave);
  }, [saveShiftToStorage]);

  // Clear shift
  const clearShift = useCallback((): void => {
    setShiftState({
      currentShift: null,
      currentShiftEntry: null,
      elapsedTime: "00:00:00",
      isActive: false,
      startTime: null,
    });
    localStorage.removeItem(STORAGE_KEY);
  }, []);

  // Refresh shift from API
  const refreshShift = useCallback(async (): Promise<void> => {
    if (!user_details?.employee_no) return;

    try {
      // Get today's date for filtering
      const today = new Date().toISOString().split('T')[0];

      // Get shifts for the current employee
      const shiftsResponse = await getShifts({
        employee: user_details.employee_no,
        start_time: today,
        page_size: 1,
        ordering: '-start_time'
      }).unwrap();

      if (shiftsResponse.results && shiftsResponse.results.length > 0) {
        const currentShift = shiftsResponse.results[0];

        // Get shift entries for this shift to find active entry
        const entriesResponse = await getShiftEntries({
          shift: currentShift.code,
          ordering: '-start_time'
        }).unwrap();

        // Find the most recent entry without end_time (active entry)
        const activeEntry = entriesResponse.results?.find(entry => !entry.end_time) || null;

        updateShift(currentShift, activeEntry);
      } else {
        clearShift();
      }
    } catch (error) {
      console.error("Error refreshing shift:", error);
      // Keep local state if API fails
    }
  }, [user_details?.employee_no, getShifts, getShiftEntries, updateShift, clearShift]);

  // Initialize shift state on mount
  useEffect(() => {
    const storedData = loadShiftFromStorage();
    if (storedData.shift || storedData.shiftEntry) {
      updateShift(storedData.shift, storedData.shiftEntry);
    }

    // Refresh from API to ensure data is current
    if (user_details?.employee_no) {
      refreshShift();
    }
  }, [user_details?.employee_no, loadShiftFromStorage, updateShift, refreshShift]);

  // Update elapsed time every second for active shifts
  useEffect(() => {
    if (!shiftState.isActive || !shiftState.currentShiftEntry?.start_time) {
      return;
    }

    const updateElapsedTime = () => {
      const elapsed = calculateElapsedTime(shiftState.currentShiftEntry!.start_time);
      setShiftState(prev => ({
        ...prev,
        elapsedTime: elapsed,
      }));
    };

    updateElapsedTime();
    const interval = setInterval(updateElapsedTime, 1000);

    return () => clearInterval(interval);
  }, [shiftState.isActive, shiftState.currentShiftEntry?.start_time, calculateElapsedTime]);

  // Sync with localStorage changes (for multiple tabs)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === STORAGE_KEY) {
        const newShift = e.newValue ? JSON.parse(e.newValue) : null;
        setShiftState(prev => ({
          ...prev,
          currentShift: newShift,
          isActive: !!newShift && !newShift.end_time,
          startTime: newShift ? new Date(newShift.start_time) : null,
        }));
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);

  return {
    shiftState,
    updateShift,
    clearShift,
    refreshShift,
    formatElapsedTime,
  };
};
